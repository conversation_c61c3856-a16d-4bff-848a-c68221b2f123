use std::collections::HashMap;
use regex::Regex;
use once_cell::sync::Lazy;

use crate::utils_classes::{
    DaemonConfig,
    DHCPConfig,
    DNSConfig,
    FilterLogConfig,
    RouterConfig,
    RouterBoardConfig,
    SnortConfig,
    SquidConfig,
    SwitchConfig,
    UserAuditConfig,
    UserNoticeConfig,
    UserWarningConfig,
    VMwareConfig,
    VPNServerConfig,
    WindowsServerConfig,
    MYSQLValue,
};

use crate::utils_patterns::{
    DAEMON_PATTERN,
    FILTERLOG_PATTERN,
    SNORT_PATTERN,
    SQUID_PATTERN,
    USERAUDIT_PATTERN,
    USERNOTICE_PATTERN,
    USERWARNING_PATTERN,
    ROUTER_PATTERN,
    ROUTERBOARD_PATTERN,
    SWITCH_PATTERN,
    VMWARE_PATTERN,
    WINDOWSSERVER_PATTERN,
    WS_AN_AD_PATTERN,
    WS_SW_PATTERN,
    DHCP_PATTERN,
    DNS_PATTERN,
    DNS_REST_PATTERN,
    VPNSERVER_PATTERN,
};

#[derive(Debug, Clone)]
pub enum ConfigType {
    FilterLog,
    Snort,
    Daemon,
    VPNServer,
    WindowsServer,
    DNS,
    DHCP,
    UserWarning,
    Switch,
    UserNotice,
    UserAudit,
    Squid,
    Router,
    RouterBoard,
    VMware,
}

fn _is_invalid_ln(ln: &str) -> bool {
    if ln.is_empty() ||
       ln.contains("ERROR name exceeds safe print buffer length") ||
       ln.contains("ERROR length byte") ||
       ln.contains("leads outside message") ||
       ln.contains("Exiting on signal") ||
       ln.contains("Now monitoring attacks") ||
       ln.contains("spp_arpspoof") ||
       ln.contains("because it is a directory, not a file") ||
       !ln.chars().next().unwrap_or(' ').is_ascii_digit() {
        return true;
    }
    false
}

fn _invalid_line_sections(
    object_list_of_names_and_addresses: &[String],
    event_types: &[String],
    filterby_is_in_alert_type: bool,
    filterby: &str,
    line_object: &str,
    line_event_type: &str,
    line_alert_type: &str,
) -> bool {
    if event_types.is_empty() && filterby.is_empty() && line_object.is_empty() {
        return false;
    }

    if !line_object.is_empty() && !object_list_of_names_and_addresses.contains(&line_object.to_string()) {
        return true;
    }

    if !event_types.is_empty() && !event_types.contains(&line_event_type.to_string()) {
        return true;
    }

    if filterby_is_in_alert_type && !line_alert_type.contains(filterby) {
        return true;
    }

    false
}

fn _extract_matches_from_pattern(string: &str, pattern: &Regex) -> Option<Vec<String>> {
    pattern.captures(string).map(|caps| {
        caps.iter()
            .skip(1)
            .map(|m| m.map_or("".to_string(), |m| m.as_str().to_string()))
            .collect()
    })
}

fn _parse_snort(
    ln: &str,
    object_list_of_names_and_addresses: &[String],
    object_dict_of_addresses_and_names: &HashMap<String, String>,
    event_types: &[String],
    filterby_is_in_alert_type: bool,
    filterby: &str,
) -> (Option<String>, Option<Vec<String>>) {
    let splited = match _extract_matches_from_pattern(ln, &SNORT_PATTERN) {
        Some(s) => s,
        None => return (None, None),
    };

    let line_sensor = &splited[2];
    let line_event_type = &splited[3];
    let line_alert_type = &splited[4];

    if _invalid_line_sections(
        object_list_of_names_and_addresses,
        event_types,
        filterby_is_in_alert_type,
        filterby,
        line_sensor,
        line_event_type,
        line_alert_type,
    ) {
        return (None, None);
    }

    let final_sensor = object_dict_of_addresses_and_names
        .get(line_sensor)
        .unwrap_or(line_sensor)
        .clone();

    let row = vec![
        splited[0].clone(),   
        splited[1].clone(),   
        splited[5].clone(),   
        splited[6].clone(),   
        splited[7].clone(),   
        splited[8].clone(),   
        splited[9].clone(),   
        splited[10].clone(),  
        splited[11].clone(),  
        splited[12].clone(),  
        splited[13].clone(),  
    ];

    (Some(final_sensor), Some(row))
}

fn _parse_daemon(
    ln: &str,
    object_list_of_names_and_addresses: &[String],
    object_dict_of_addresses_and_names: &HashMap<String, String>,
    event_types: &[String],
    filterby_is_in_alert_type: bool,
    filterby: &str,
) -> (Option<String>, Option<Vec<String>>) {
    let splited = match _extract_matches_from_pattern(ln, &DAEMON_PATTERN) {
        Some(s) => s,
        None => return (None, None),
    };

    let line_sensor = &splited[2];
    let line_event_type = &splited[3];
    let line_alert_type = &splited[4];

    if _invalid_line_sections(
        object_list_of_names_and_addresses,
        event_types,
        filterby_is_in_alert_type,
        filterby,
        line_sensor,
        line_event_type,
        line_alert_type,
    ) {
        return (None, None);
    }

    let final_sensor = object_dict_of_addresses_and_names
        .get(line_sensor)
        .unwrap_or(line_sensor)
        .clone();

    let row = vec![
        splited[0].clone(),  
        splited[1].clone(),  
        line_event_type.clone(),
        line_alert_type.clone(),
        splited[5].clone(),  
    ];

    (Some(final_sensor), Some(row))
}

static _PRECOMPUTED_VALUES: Lazy<HashMap<&'static str, (String, String, bool, Vec<String>)>> = Lazy::new(|| {
    let mut map = HashMap::new();

    let extract_config_values = |config_type: ConfigType| -> (String, String, bool, Vec<String>) {
        match config_type {
            ConfigType::FilterLog => {
                let slug = if let MYSQLValue::Str(s) = FilterLogConfig::SLUG.value() { s } else { String::new() };
                let filterby = if let MYSQLValue::Str(s) = FilterLogConfig::FILTERBY.value() { s } else { String::new() };
                let filterby_is_in_alert_type = if let MYSQLValue::Bool(b) = FilterLogConfig::FILTERBY_IS_IN_ALERT_TYPE.value() { b } else { false };
                let event_types = if let MYSQLValue::List(l) = FilterLogConfig::EVENT_TYPES.value() { l } else { vec![] };
                (slug, filterby, filterby_is_in_alert_type, event_types)
            },
            ConfigType::Snort => {
                let slug = if let MYSQLValue::Str(s) = SnortConfig::SLUG.value() { s } else { String::new() };
                let filterby = if let MYSQLValue::Str(s) = SnortConfig::FILTERBY.value() { s } else { String::new() };
                let filterby_is_in_alert_type = if let MYSQLValue::Bool(b) = SnortConfig::FILTERBY_IS_IN_ALERT_TYPE.value() { b } else { false };
                let event_types = if let MYSQLValue::List(l) = SnortConfig::EVENT_TYPES.value() { l } else { vec![] };
                (slug, filterby, filterby_is_in_alert_type, event_types)
            },
            ConfigType::Daemon => {
                let slug = if let MYSQLValue::Str(s) = DaemonConfig::SLUG.value() { s } else { String::new() };
                let filterby = if let MYSQLValue::Str(s) = DaemonConfig::FILTERBY.value() { s } else { String::new() };
                let filterby_is_in_alert_type = if let MYSQLValue::Bool(b) = DaemonConfig::FILTERBY_IS_IN_ALERT_TYPE.value() { b } else { false };
                let event_types = if let MYSQLValue::List(l) = DaemonConfig::EVENT_TYPES.value() { l } else { vec![] };
                (slug, filterby, filterby_is_in_alert_type, event_types)
            },
            ConfigType::VPNServer => {
                let slug = if let MYSQLValue::Str(s) = VPNServerConfig::SLUG.value() { s } else { String::new() };
                let filterby = if let MYSQLValue::Str(s) = VPNServerConfig::FILTERBY.value() { s } else { String::new() };
                let filterby_is_in_alert_type = if let MYSQLValue::Bool(b) = VPNServerConfig::FILTERBY_IS_IN_ALERT_TYPE.value() { b } else { false };
                let event_types = if let MYSQLValue::List(l) = VPNServerConfig::EVENT_TYPES.value() { l } else { vec![] };
                (slug, filterby, filterby_is_in_alert_type, event_types)
            },
            ConfigType::WindowsServer => {
                let slug = if let MYSQLValue::Str(s) = WindowsServerConfig::SLUG.value() { s } else { String::new() };
                let filterby = if let MYSQLValue::Str(s) = WindowsServerConfig::FILTERBY.value() { s } else { String::new() };
                let filterby_is_in_alert_type = if let MYSQLValue::Bool(b) = WindowsServerConfig::FILTERBY_IS_IN_ALERT_TYPE.value() { b } else { false };
                let event_types = if let MYSQLValue::List(l) = WindowsServerConfig::EVENT_TYPES.value() { l } else { vec![] };
                (slug, filterby, filterby_is_in_alert_type, event_types)
            },
            ConfigType::DNS => {
                let slug = if let MYSQLValue::Str(s) = DNSConfig::SLUG.value() { s } else { String::new() };
                let filterby = if let MYSQLValue::Str(s) = DNSConfig::FILTERBY.value() { s } else { String::new() };
                let filterby_is_in_alert_type = if let MYSQLValue::Bool(b) = DNSConfig::FILTERBY_IS_IN_ALERT_TYPE.value() { b } else { false };
                let event_types = if let MYSQLValue::List(l) = DNSConfig::EVENT_TYPES.value() { l } else { vec![] };
                (slug, filterby, filterby_is_in_alert_type, event_types)
            },
            ConfigType::DHCP => {
                let slug = if let MYSQLValue::Str(s) = DHCPConfig::SLUG.value() { s } else { String::new() };
                let filterby = if let MYSQLValue::Str(s) = DHCPConfig::FILTERBY.value() { s } else { String::new() };
                let filterby_is_in_alert_type = if let MYSQLValue::Bool(b) = DHCPConfig::FILTERBY_IS_IN_ALERT_TYPE.value() { b } else { false };
                let event_types = if let MYSQLValue::List(l) = DHCPConfig::EVENT_TYPES.value() { l } else { vec![] };
                (slug, filterby, filterby_is_in_alert_type, event_types)
            },
            ConfigType::UserWarning => {
                let slug = if let MYSQLValue::Str(s) = UserWarningConfig::SLUG.value() { s } else { String::new() };
                let filterby = if let MYSQLValue::Str(s) = UserWarningConfig::FILTERBY.value() { s } else { String::new() };
                let filterby_is_in_alert_type = if let MYSQLValue::Bool(b) = UserWarningConfig::FILTERBY_IS_IN_ALERT_TYPE.value() { b } else { false };
                let event_types = if let MYSQLValue::List(l) = UserWarningConfig::EVENT_TYPES.value() { l } else { vec![] };
                (slug, filterby, filterby_is_in_alert_type, event_types)
            },
            ConfigType::Switch => {
                let slug = if let MYSQLValue::Str(s) = SwitchConfig::SLUG.value() { s } else { String::new() };
                let filterby = if let MYSQLValue::Str(s) = SwitchConfig::FILTERBY.value() { s } else { String::new() };
                let filterby_is_in_alert_type = if let MYSQLValue::Bool(b) = SwitchConfig::FILTERBY_IS_IN_ALERT_TYPE.value() { b } else { false };
                let event_types = if let MYSQLValue::List(l) = SwitchConfig::EVENT_TYPES.value() { l } else { vec![] };
                (slug, filterby, filterby_is_in_alert_type, event_types)
            },
            ConfigType::UserNotice => {
                let slug = if let MYSQLValue::Str(s) = UserNoticeConfig::SLUG.value() { s } else { String::new() };
                let filterby = if let MYSQLValue::Str(s) = UserNoticeConfig::FILTERBY.value() { s } else { String::new() };
                let filterby_is_in_alert_type = if let MYSQLValue::Bool(b) = UserNoticeConfig::FILTERBY_IS_IN_ALERT_TYPE.value() { b } else { false };
                let event_types = if let MYSQLValue::List(l) = UserNoticeConfig::EVENT_TYPES.value() { l } else { vec![] };
                (slug, filterby, filterby_is_in_alert_type, event_types)
            },
            ConfigType::UserAudit => {
                let slug = if let MYSQLValue::Str(s) = UserAuditConfig::SLUG.value() { s } else { String::new() };
                let filterby = if let MYSQLValue::Str(s) = UserAuditConfig::FILTERBY.value() { s } else { String::new() };
                let filterby_is_in_alert_type = if let MYSQLValue::Bool(b) = UserAuditConfig::FILTERBY_IS_IN_ALERT_TYPE.value() { b } else { false };
                let event_types = if let MYSQLValue::List(l) = UserAuditConfig::EVENT_TYPES.value() { l } else { vec![] };
                (slug, filterby, filterby_is_in_alert_type, event_types)
            },
            ConfigType::Squid => {
                let slug = if let MYSQLValue::Str(s) = SquidConfig::SLUG.value() { s } else { String::new() };
                let filterby = if let MYSQLValue::Str(s) = SquidConfig::FILTERBY.value() { s } else { String::new() };
                let filterby_is_in_alert_type = if let MYSQLValue::Bool(b) = SquidConfig::FILTERBY_IS_IN_ALERT_TYPE.value() { b } else { false };
                let event_types = if let MYSQLValue::List(l) = SquidConfig::EVENT_TYPES.value() { l } else { vec![] };
                (slug, filterby, filterby_is_in_alert_type, event_types)
            },
            ConfigType::Router => {
                let slug = if let MYSQLValue::Str(s) = RouterConfig::SLUG.value() { s } else { String::new() };
                let filterby = if let MYSQLValue::Str(s) = RouterConfig::FILTERBY.value() { s } else { String::new() };
                let filterby_is_in_alert_type = if let MYSQLValue::Bool(b) = RouterConfig::FILTERBY_IS_IN_ALERT_TYPE.value() { b } else { false };
                let event_types = if let MYSQLValue::List(l) = RouterConfig::EVENT_TYPES.value() { l } else { vec![] };
                (slug, filterby, filterby_is_in_alert_type, event_types)
            },
            ConfigType::RouterBoard => {
                let slug = if let MYSQLValue::Str(s) = RouterBoardConfig::SLUG.value() { s } else { String::new() };
                let filterby = if let MYSQLValue::Str(s) = RouterBoardConfig::FILTERBY.value() { s } else { String::new() };
                let filterby_is_in_alert_type = if let MYSQLValue::Bool(b) = RouterBoardConfig::FILTERBY_IS_IN_ALERT_TYPE.value() { b } else { false };
                let event_types = if let MYSQLValue::List(l) = RouterBoardConfig::EVENT_TYPES.value() { l } else { vec![] };
                (slug, filterby, filterby_is_in_alert_type, event_types)
            },
            ConfigType::VMware => {
                let slug = if let MYSQLValue::Str(s) = VMwareConfig::SLUG.value() { s } else { String::new() };
                let filterby = if let MYSQLValue::Str(s) = VMwareConfig::FILTERBY.value() { s } else { String::new() };
                let filterby_is_in_alert_type = if let MYSQLValue::Bool(b) = VMwareConfig::FILTERBY_IS_IN_ALERT_TYPE.value() { b } else { false };
                let event_types = if let MYSQLValue::List(l) = VMwareConfig::EVENT_TYPES.value() { l } else { vec![] };
                (slug, filterby, filterby_is_in_alert_type, event_types)
            },
        }
    };

    let configs = [
        (ConfigType::FilterLog, "filterlog"),
        (ConfigType::Snort, "snort"),
        (ConfigType::Daemon, "daemon"),
        (ConfigType::VPNServer, "vpnserver"),
        (ConfigType::WindowsServer, "windowsserver"),
        (ConfigType::DNS, "dns"),
        (ConfigType::DHCP, "dhcp"),
        (ConfigType::UserWarning, "userwarning"),
        (ConfigType::Switch, "switch"),
        (ConfigType::UserNotice, "usernotice"),
        (ConfigType::UserAudit, "useraudit"),
        (ConfigType::Squid, "squid"),
        (ConfigType::Router, "router"),
        (ConfigType::RouterBoard, "routerboard"),
        (ConfigType::VMware, "vmware"),
    ];

    for (config_type, key) in configs {
        let values = extract_config_values(config_type);
        map.insert(key, values);
    }

    map
});

static _PARSERS: Lazy<HashMap<&'static str, fn(&str, &[String], &HashMap<String, String>, &[String], bool, &str) -> (Option<String>, Option<Vec<String>>)>> = Lazy::new(|| {
    let mut map = HashMap::new();
    map.insert("snort", _parse_snort as fn(&str, &[String], &HashMap<String, String>, &[String], bool, &str) -> (Option<String>, Option<Vec<String>>));
    map.insert("daemon", _parse_daemon as fn(&str, &[String], &HashMap<String, String>, &[String], bool, &str) -> (Option<String>, Option<Vec<String>>));
    map
});

pub fn parse_ln(
    ln: &str,
    cls: ConfigType,
    object_list_of_names_and_addresses: &[String],
    object_dict_of_addresses_and_names: &HashMap<String, String>,
) -> (Option<String>, Option<Vec<String>>) {
    if _is_invalid_ln(ln) {
        return (None, None);
    }

    let slug_key = match cls {
        ConfigType::FilterLog => "filterlog",
        ConfigType::Snort => "snort",
        ConfigType::Daemon => "daemon",
        ConfigType::VPNServer => "vpnserver",
        ConfigType::WindowsServer => "windowsserver",
        ConfigType::DNS => "dns",
        ConfigType::DHCP => "dhcp",
        ConfigType::UserWarning => "userwarning",
        ConfigType::Switch => "switch",
        ConfigType::UserNotice => "usernotice",
        ConfigType::UserAudit => "useraudit",
        ConfigType::Squid => "squid",
        ConfigType::Router => "router",
        ConfigType::RouterBoard => "routerboard",
        ConfigType::VMware => "vmware",
    };

    let (slug, filterby, filterby_is_in_alert_type, event_types) = match _PRECOMPUTED_VALUES.get(slug_key) {
        Some(values) => values,
        None => return (None, None),
    };

    if !filterby.is_empty() && !filterby_is_in_alert_type && !ln.contains(filterby) {
        return (None, None);
    }

    let func = match _PARSERS.get(slug.as_str()) {
        Some(f) => f,
        None => return (None, None),
    };

    func(
        ln,
        object_list_of_names_and_addresses,
        object_dict_of_addresses_and_names,
        event_types,
        *filterby_is_in_alert_type,
        filterby,
    )
}
